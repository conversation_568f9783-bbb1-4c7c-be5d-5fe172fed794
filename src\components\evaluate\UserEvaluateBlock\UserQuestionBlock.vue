<template>
  <q-card v-if="category != 'HEADER' && matchedItem" class="q-pa-md q-ma-md evaluate-get">
    <div class="row q-ma-md">
      <q-markdown class="title">
        {{ matchedItem?.questions?.[0]?.questionText || 'ไม่พบคำถาม' }}
      </q-markdown>
      <div class="required">*</div>
    </div>
    <div v-if="matchedItem.questions?.[0]?.imagePath" class="row justify-center q-ma-md">
      <q-img
        :src="matchedItem.questions?.[0]?.imagePath"
        fit="scale-down"
        :ratio="1"
        class="centered-image"
        :width="
          matchedItem.questions?.[0]?.imageWidth
            ? matchedItem.questions[0].imageWidth + 'px'
            : undefined
        "
        :height="
          matchedItem.questions?.[0]?.imageHeight
            ? matchedItem.questions[0].imageHeight + 'px'
            : '200px'
        "
      />
    </div>

    <div class="row q-ma-md">
      <div v-if="category === 'RADIO'" class="group font-size">
        <q-radio
          :disable="isPreview"
          v-model="selectedAnswer"
          v-for="choice in matchedItem?.options"
          :key="choice.id ?? choice.optionText"
          :val="choice.id"
          :label="choice.optionText"
          color="primary"
          @update:model-value="emitAnswer"
          @blur="handleBlur"
        />
      </div>

      <div v-else-if="category === 'CHECKBOX'" class="group font-size">
        <q-checkbox
          v-for="choice in matchedItem?.options"
          :key="choice.id"
          :val="choice.id"
          v-model="selectedAnswers"
          :label="choice.optionText"
          color="primary"
          :disable="isPreview"
          @update:model-value="onCheckboxChange"
        />
      </div>

      <div v-else-if="category === 'TEXTFIELD'">
        <q-input
          :disable="isPreview"
          v-model="textAnswer"
          dense
          placeholder="คำตอบ..."
          style="min-width: 400px"
          class="font-size"
          @blur="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'GRID'" class="grid-choice">
        <q-table flat bordered :rows="rows" :columns="columns" row-key="id" hide-bottom>
          <template v-slot:body-cell="props">
            <q-td :props="props">
              <template v-if="props.col.name === 'question'">
                {{ props.row.question }}
              </template>
              <template v-else>
                <q-radio
                  :disable="isPreview"
                  :val="Number(props.col.name.replace('choice_', ''))"
                  v-model="gridAnswers[props.row.id]"
                  size="sm"
                  color="primary"
                />
              </template>
            </q-td>
          </template>
        </q-table>
      </div>
      <div v-else-if="category === 'UPLOAD'">
        <div class="q-mb-md" style="color: #9d9d9d">
          {{
            'อัปโหลด ' +
            matchedItem?.questions?.[0]?.sizeLimit +
            ' ไฟล์ ' +
            matchedItem?.questions?.[0]?.acceptFile +
            ' มากสุด ' +
            matchedItem?.questions?.[0]?.uploadLimit
          }}
        </div>
        <q-btn
          :disable="isPreview"
          icon="file_upload"
          label="เพิ่มไฟล์"
          class="file-upload font-size"
        />
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import type { Assessment, Option } from 'src/types/models';
import type { Response } from 'src/types/models';
import { ResponsesService } from 'src/services/asm/responseService';

import { computed, onMounted, ref, watch } from 'vue';
import { OptionService } from 'src/services/asm/optionService';
const props = defineProps<{
  id: number;
  draftId: number;
  category: string;
  item: Assessment;
  section: number;
  status: boolean;
}>();

//answer
const selectedAnswer = ref<string>('');
const selectedAnswers = ref<string[]>([]);
const textAnswer = ref('');
const gridAnswers = ref<{ [key: number]: number }>({});
const lastGridAnswer = ref<{ [key: number]: number }>();
const response = ref<Response>();

const isPreview = ref(true);
const matchedItem = computed(() =>
  props.item.itemBlocks?.find((item) => item.id === props.id && item.section === props.section),
);

if (props.status === true) {
  isPreview.value = false;
}
type AnswerValue = string | string[] | Record<string, number>;

//checkbox
let previousSelected: string[] = [];

const onCheckboxChange = async (newVal: string[]) => {
  // ตรวจหาว่ามีอะไรเพิ่มเข้ามาหรือถูกลบไป
  const added = newVal.find((val) => !previousSelected.includes(val));
  const removed = previousSelected.find((val) => !newVal.includes(val));

  if (added) {
    try {
      const questionId = matchedItem.value?.questions?.[0]?.id;

      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        console.log('in q');
        if (res) {
          response.value = res;
        }
        if (response.value) {
          console.log('in r');
          for (const optionId of added) {
            const payload = {
              id: response.value.id,
              submissionId: props.draftId,
              questionId,
              selectedOptionId: optionId, // อันนี้เป็นรายตัว
            };
            console.trace('เพิ่มข้อมูล:', payload);
          }
          if (removed) {
            for (const optionId of removed) {
              const payload = {
                id: response.value.id,
                submissionId: props.draftId,
                questionId,
                selectedOptionId: optionId, // อันนี้เป็นรายตัว
              };
              console.trace('ลบข้อมูล:', payload);
            }
          }
        }
      }
    } catch (err) {
      console.error('เพิ่มข้อมูลไม่สำเร็จ', err);
    }
  }
  // เก็บค่าปัจจุบันไว้เปรียบเทียบรอบหน้า
  previousSelected = [...newVal];
  console.log(previousSelected);
};

const emit = defineEmits<{
  (event: 'update-answer', payload: { id: string | number; value: AnswerValue }): void;
}>();
emit('update-answer', {
  id: props.id,
  value:
    props.category === 'CHECKBOX'
      ? selectedAnswers.value
      : props.category === 'GRID'
        ? gridAnswers.value
        : props.category === 'RADIO'
          ? selectedAnswer.value
          : props.category === 'TEXTFIELD'
            ? textAnswer.value
            : '', // fallback กรณีไม่ตรงกับเงื่อนไขใดเลย
});

// Emit คำตอบเมื่อมีการเปลี่ยนแปลง

const emitAnswer = async () => {
  let value;

  if (props.category === 'GRID') {
    console.log(gridAnswers.value);
    const entries = Object.entries(gridAnswers.value); // [[key, value], ...]

    const lastEntry = entries.at(-1); // หรือใช้ entries[entries.length - 1]
    if (lastEntry) {
      const [lastKey, lastValue] = lastEntry;
      const option = matchedItem.value?.options?.[Number(lastValue)];
      const realOption = option?.id;
      if (realOption) {
        lastGridAnswer.value = { [parseInt(lastKey, 10)]: realOption };
      } else {
        return;
      }
      value = lastGridAnswer.value;
      console.log('value: ', value);
    } else {
      lastGridAnswer.value = {};
      value = '';
    }
  } else if (props.category === 'CHECKBOX') {
    value = selectedAnswers.value; // เช่น [1, 2]
  } else if (props.category === 'TEXTFILED') {
    value = textAnswer.value;
  } else if (props.category === 'RADIO') {
    value = selectedAnswer.value;
  } else {
    value = textAnswer.value;
  }

  emit('update-answer', {
    id: props.id,
    value,
  });

  try {
    if (props.category === 'TEXTFIELD') {
      const questionId = matchedItem.value?.questions?.[0]?.id;
      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res;
        }
        if (response.value?.id && response.value.selectedOptionId) {
          const option = await new OptionService().getOptionById(response.value.selectedOptionId);
          const upDateAnswerText: Option = {
            id: option.id,
            itemBlockId: option.itemBlockId,
            optionText: textAnswer.value,
            value: option.value,
            sequence: option.sequence,
          };
          await new OptionService().updateOption(response.value.selectedOptionId, upDateAnswerText);
        } else {
          const answerText: Option = {
            id: 0,
            itemBlockId: props.id,
            optionText: textAnswer.value,
            value: 0,
            sequence: 0,
          };
          const createdOption = await new OptionService().createOption(answerText);

          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: createdOption.id,
          };
          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'RADIO') {
      const questionId = matchedItem.value?.questions?.[0]?.id;

      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res;
        }
        if (response.value?.id && response.value.questionId) {
          console.log('update');
          const updatePayload: Response = {
            id: response.value.id,
            submissionId: response.value.submissionId,
            questionId: response.value.questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };
          console.trace('emitAnswer called', updatePayload);
          await new ResponsesService().update(updatePayload.id, updatePayload);
        } else {
          console.log('create');
          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };

          console.trace('emitAnswer called', payload);
          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'GRID') {
      const [questionIdStr, selectedOptionId] = Object.entries(value).at(-1) || [];
      const questionId = Number(questionIdStr ?? 0);
      const res = await new ResponsesService().findAnswer(props.draftId, questionId);
      if (res) {
        response.value = res;
      }
      console.log(response.value);
      if (response.value?.id) {
        console.log('update');
        const updatePayload: Response = {
          id: response.value.id,
          submissionId: response.value.submissionId,
          questionId,
          selectedOptionId,
        };
        console.trace('emitAnswer called', updatePayload);
        await new ResponsesService().update(updatePayload.id, updatePayload);
      } else {
        console.log('create');
        const payload: Response = {
          id: 0,
          submissionId: props.draftId,
          questionId,
          selectedOptionId,
        };

        console.trace('emitAnswer called', payload);
        await new ResponsesService().create(payload);
      }
    }
  } catch (error) {
    console.error('Failed to send response to backend:', error);
  }
};
watch(
  gridAnswers,
  async () => {
    await emitAnswer();
  },
  { deep: true },
);

watch(
  matchedItem,
  (newItem) => {
    if (newItem) {
      newItem.section = props.section;
    }
  },
  { immediate: true },
);

interface Row {
  id: number;
  question: string;
  [key: `choice_${number}`]: string;
}

const columns = computed<QTableColumn[]>(() => {
  const base: QTableColumn[] = [
    {
      name: 'question',
      label: 'คำถาม',
      field: 'question',
      align: 'left',
    },
  ];

  const choices: QTableColumn[] =
    matchedItem.value?.options?.map((opt, i) => ({
      name: `choice_${i}`,
      label: opt.optionText,
      field: `choice_${i}`,
      align: 'center',
    })) ?? [];

  return base.concat(choices);
});

const rows = computed<Row[]>(() => {
  if (!matchedItem.value?.questions) return [];

  return matchedItem.value.questions
    .filter((q) => !q.isHeader) // กรองเอาเฉพาะคำถามที่ไม่ใช่ header
    .map((q) => {
      const row: Row = {
        id: q.id,
        question: q.questionText,
      };

      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });
      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });

      return row;
    });
});

const STORAGE_KEY = 'draft-form';

onMounted(() => {
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) {
    const parsed = JSON.parse(saved);
    if (props.category === 'checkbox') {
      selectedAnswers.value = parsed;
    } else if (props.category === 'multipleGridChoice') {
      gridAnswers.value = parsed;
    } else {
      selectedAnswer.value = parsed;
    }
  }
});

// blur → save (เฉพาะ shortAnswer เพราะ radio/checkbox ไม่มี blur)
function handleBlur() {}
</script>

<style scoped lang="scss">
.title {
  font-size: 20px;
}

.required {
  color: red;
  font-size: 20px;
  margin-left: 4px;
}
.group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-upload {
  background-color: white;
  color: $primary;
  border: 1px solid;
  border-color: $surface;
}

.font-size {
  font-size: 18px;
}
.grid-choice {
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-table {
  width: 100%;
  max-width: 800px;
  table-layout: fixed;

  .label-column {
    width: 50%;
    max-width: 50%;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .option-column {
    width: calc(50% / 5);
    padding: 8px;
    text-align: center;

    &:first-child {
      border-left: none;
    }
  }
}
</style>
