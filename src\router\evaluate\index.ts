import type { RouteRecordRaw } from 'vue-router';

const evaluateRoutes: RouteRecordRaw[] = [
  {
    path: '/evaluate',
    name: 'evaluate',
    component: () => import('../../layouts/AsmLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/evaluate') {
        next({ name: 'evaluate-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'evaluate-management',
        component: () => import('../../pages/evaluate/EvaluateManagementPage.vue'),
        meta: {},
      },
      {
        path: ':id(\\d+)/edit',
        name: 'evaluate-edit',
        component: () => import('../../pages/evaluate/EvaluateEditorPage.vue'),
        props: true,
      },
      {
        path: ':url/:section',
        name: 'evaluate-preview',
        component: () => import('../../pages/evaluate/EvaluateIdPage.vue'),
        props: true,
      },
      {
        path: ':url/:section',
        name: 'evaluate-do',
        component: () => import('../../pages/evaluate/EvaluateIdPage.vue'),
        props: true,
      },
    ],
  },
];

export default evaluateRoutes;
